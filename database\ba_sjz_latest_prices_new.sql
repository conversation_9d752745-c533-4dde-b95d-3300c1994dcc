-- ----------------------------
-- 新的物品最新价格表结构 (扩展版本)
-- 包含更多时间维度和价格分析指标
-- ----------------------------

DROP TABLE IF EXISTS `ba_sjz_latest_prices_new`;
CREATE TABLE `ba_sjz_latest_prices_new`  (
  `object_id` bigint(20) NOT NULL COMMENT '物品ID',
  
  -- 基础价格信息
  `current_price` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '当前价格',
  `price_24h_ago` decimal(12, 2) NULL DEFAULT NULL COMMENT '24小时前价格',
  `price_7d_ago` decimal(12, 2) NULL DEFAULT NULL COMMENT '7天前价格',
  `price_30d_ago` decimal(12, 2) NULL DEFAULT NULL COMMENT '30天前价格',
  `price_1h_ago` decimal(12, 2) NULL DEFAULT NULL COMMENT '1小时前价格',
  
  -- 价格变化统计
  `price_change_24h` decimal(12, 2) NULL DEFAULT NULL COMMENT '24小时价格变化金额',
  `price_change_24h_percent` decimal(8, 4) NULL DEFAULT NULL COMMENT '24小时价格变化百分比',
  `price_change_7d` decimal(12, 2) NULL DEFAULT NULL COMMENT '7天价格变化金额',
  `price_change_7d_percent` decimal(8, 4) NULL DEFAULT NULL COMMENT '7天价格变化百分比',
  
  -- 价格极值
  `highest_price_24h` decimal(12, 2) NULL DEFAULT NULL COMMENT '24小时内最高价',
  `lowest_price_24h` decimal(12, 2) NULL DEFAULT NULL COMMENT '24小时内最低价',
  `highest_price_7d` decimal(12, 2) NULL DEFAULT NULL COMMENT '7天内最高价',
  `lowest_price_7d` decimal(12, 2) NULL DEFAULT NULL COMMENT '7天内最低价',
  
  -- 统计指标
  `avg_price_24h` decimal(12, 2) NULL DEFAULT NULL COMMENT '24小时平均价格',
  `price_trend` varchar(10) NULL DEFAULT NULL COMMENT '价格趋势（上涨/下跌/平稳）',
  `volatility_24h` decimal(8, 4) NULL DEFAULT NULL COMMENT '24小时波动率',
  
  -- 时间戳
  `last_update_timestamp` datetime NOT NULL COMMENT '最后价格更新时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  PRIMARY KEY (`object_id`) USING BTREE,
  INDEX `idx_latest_prices_object`(`object_id` ASC) USING BTREE,
  INDEX `idx_current_price_desc`(`current_price` DESC) USING BTREE,
  INDEX `idx_object_id_price`(`object_id` ASC, `current_price` ASC) USING BTREE,
  INDEX `idx_price_change_24h`(`price_change_24h_percent` DESC) USING BTREE,
  INDEX `idx_price_trend`(`price_trend` ASC) USING BTREE,
  INDEX `idx_volatility_24h`(`volatility_24h` DESC) USING BTREE,
  INDEX `idx_highest_price_24h`(`highest_price_24h` DESC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '物品最新价格表(扩展版本)' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 数据迁移脚本：从旧表迁移到新表
-- ----------------------------

-- 插入现有数据到新表
INSERT INTO ba_sjz_latest_prices_new (
    object_id, 
    current_price, 
    price_24h_ago, 
    last_update_timestamp, 
    update_time
)
SELECT 
    object_id,
    current_price,
    price_24h_ago,
    last_update_timestamp,
    update_time
FROM ba_sjz_latest_prices;

-- ----------------------------
-- 更新扩展价格维度的存储过程
-- ----------------------------

DROP PROCEDURE IF EXISTS `update_extended_price_metrics`;
delimiter ;;
CREATE PROCEDURE `update_extended_price_metrics`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_object_id BIGINT;
    DECLARE cur CURSOR FOR SELECT DISTINCT object_id FROM ba_sjz_latest_prices_new;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_object_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 更新各时间维度的价格
        UPDATE ba_sjz_latest_prices_new lp
        SET 
            -- 1小时前价格
            price_1h_ago = (
                SELECT ph.price 
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp <= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ORDER BY ph.timestamp DESC 
                LIMIT 1
            ),
            
            -- 7天前价格
            price_7d_ago = (
                SELECT ph.price 
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp <= DATE_SUB(NOW(), INTERVAL 7 DAY)
                ORDER BY ph.timestamp DESC 
                LIMIT 1
            ),
            
            -- 30天前价格
            price_30d_ago = (
                SELECT ph.price 
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp <= DATE_SUB(NOW(), INTERVAL 30 DAY)
                ORDER BY ph.timestamp DESC 
                LIMIT 1
            ),
            
            -- 24小时内最高价
            highest_price_24h = (
                SELECT MAX(ph.price) 
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ),
            
            -- 24小时内最低价
            lowest_price_24h = (
                SELECT MIN(ph.price) 
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ),
            
            -- 7天内最高价
            highest_price_7d = (
                SELECT MAX(ph.price) 
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            ),
            
            -- 7天内最低价
            lowest_price_7d = (
                SELECT MIN(ph.price) 
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            ),
            
            -- 24小时平均价格
            avg_price_24h = (
                SELECT AVG(ph.price) 
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            )
        WHERE lp.object_id = v_object_id;
        
        -- 计算价格变化和百分比
        UPDATE ba_sjz_latest_prices_new lp
        SET 
            price_change_24h = CASE 
                WHEN price_24h_ago IS NOT NULL THEN current_price - price_24h_ago 
                ELSE NULL 
            END,
            price_change_24h_percent = CASE 
                WHEN price_24h_ago IS NOT NULL AND price_24h_ago > 0 
                THEN ((current_price - price_24h_ago) / price_24h_ago) * 100 
                ELSE NULL 
            END,
            price_change_7d = CASE 
                WHEN price_7d_ago IS NOT NULL THEN current_price - price_7d_ago 
                ELSE NULL 
            END,
            price_change_7d_percent = CASE 
                WHEN price_7d_ago IS NOT NULL AND price_7d_ago > 0 
                THEN ((current_price - price_7d_ago) / price_7d_ago) * 100 
                ELSE NULL 
            END
        WHERE lp.object_id = v_object_id;
        
        -- 计算价格趋势
        UPDATE ba_sjz_latest_prices_new lp
        SET 
            price_trend = CASE 
                WHEN price_change_24h_percent IS NULL THEN '未知'
                WHEN price_change_24h_percent > 5 THEN '上涨'
                WHEN price_change_24h_percent < -5 THEN '下跌'
                ELSE '平稳'
            END
        WHERE lp.object_id = v_object_id;
        
        -- 计算24小时波动率 (标准差/平均价格)
        UPDATE ba_sjz_latest_prices_new lp
        SET 
            volatility_24h = (
                SELECT 
                    CASE 
                        WHEN AVG(ph.price) > 0 THEN 
                            (STDDEV(ph.price) / AVG(ph.price)) * 100
                        ELSE NULL 
                    END
                FROM ba_sjz_price_history ph 
                WHERE ph.object_id = v_object_id 
                AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                HAVING COUNT(*) > 1
            )
        WHERE lp.object_id = v_object_id;
        
    END LOOP;
    
    CLOSE cur;
END;;
delimiter ;

-- ----------------------------
-- 单个物品的快速更新存储过程
-- ----------------------------

DROP PROCEDURE IF EXISTS `update_single_item_price_metrics`;
delimiter ;;
CREATE PROCEDURE `update_single_item_price_metrics`(IN p_object_id BIGINT)
BEGIN
    -- 更新指定物品的各时间维度价格
    UPDATE ba_sjz_latest_prices_new lp
    SET
        -- 1小时前价格
        price_1h_ago = (
            SELECT ph.price
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp <= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY ph.timestamp DESC
            LIMIT 1
        ),

        -- 7天前价格
        price_7d_ago = (
            SELECT ph.price
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp <= DATE_SUB(NOW(), INTERVAL 7 DAY)
            ORDER BY ph.timestamp DESC
            LIMIT 1
        ),

        -- 30天前价格
        price_30d_ago = (
            SELECT ph.price
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp <= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ORDER BY ph.timestamp DESC
            LIMIT 1
        ),

        -- 24小时内最高价
        highest_price_24h = (
            SELECT MAX(ph.price)
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ),

        -- 24小时内最低价
        lowest_price_24h = (
            SELECT MIN(ph.price)
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ),

        -- 7天内最高价
        highest_price_7d = (
            SELECT MAX(ph.price)
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ),

        -- 7天内最低价
        lowest_price_7d = (
            SELECT MIN(ph.price)
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ),

        -- 24小时平均价格
        avg_price_24h = (
            SELECT AVG(ph.price)
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        )
    WHERE lp.object_id = p_object_id;

    -- 计算价格变化和百分比
    UPDATE ba_sjz_latest_prices_new lp
    SET
        price_change_24h = CASE
            WHEN price_24h_ago IS NOT NULL THEN current_price - price_24h_ago
            ELSE NULL
        END,
        price_change_24h_percent = CASE
            WHEN price_24h_ago IS NOT NULL AND price_24h_ago > 0
            THEN ((current_price - price_24h_ago) / price_24h_ago) * 100
            ELSE NULL
        END,
        price_change_7d = CASE
            WHEN price_7d_ago IS NOT NULL THEN current_price - price_7d_ago
            ELSE NULL
        END,
        price_change_7d_percent = CASE
            WHEN price_7d_ago IS NOT NULL AND price_7d_ago > 0
            THEN ((current_price - price_7d_ago) / price_7d_ago) * 100
            ELSE NULL
        END
    WHERE lp.object_id = p_object_id;

    -- 计算价格趋势
    UPDATE ba_sjz_latest_prices_new lp
    SET
        price_trend = CASE
            WHEN price_change_24h_percent IS NULL THEN '未知'
            WHEN price_change_24h_percent > 5 THEN '上涨'
            WHEN price_change_24h_percent < -5 THEN '下跌'
            ELSE '平稳'
        END
    WHERE lp.object_id = p_object_id;

    -- 计算24小时波动率
    UPDATE ba_sjz_latest_prices_new lp
    SET
        volatility_24h = (
            SELECT
                CASE
                    WHEN AVG(ph.price) > 0 THEN
                        (STDDEV(ph.price) / AVG(ph.price)) * 100
                    ELSE NULL
                END
            FROM ba_sjz_price_history ph
            WHERE ph.object_id = p_object_id
            AND ph.timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            HAVING COUNT(*) > 1
        )
    WHERE lp.object_id = p_object_id;
END;;
delimiter ;

-- ----------------------------
-- 新的触发器：在价格历史插入时更新扩展指标
-- ----------------------------

DROP TRIGGER IF EXISTS `after_price_history_insert_extended`;
delimiter ;;
CREATE TRIGGER `after_price_history_insert_extended` AFTER INSERT ON `ba_sjz_price_history` FOR EACH ROW 
BEGIN
    -- 更新或插入最新价格记录
    INSERT INTO ba_sjz_latest_prices_new (object_id, current_price, last_update_timestamp)
    VALUES (NEW.object_id, NEW.price, NEW.timestamp)
    ON DUPLICATE KEY UPDATE 
        -- 只有当新记录的时间戳更新时才更新价格
        current_price = IF(NEW.timestamp > last_update_timestamp, NEW.price, current_price),
        last_update_timestamp = IF(NEW.timestamp > last_update_timestamp, NEW.timestamp, last_update_timestamp);
        
    -- 异步更新扩展指标 (可以考虑用定时任务替代)
    -- CALL update_extended_price_metrics_single(NEW.object_id);
END;;
delimiter ;
